// 中国（含港澳台）城市数据
// 按首字母排序，包含城市名称、首字母、是否热门城市标记
const chineseCities = [
  // A
  { name: "安庆", firstLetter: "A", isHot: false },
  { name: "安阳", firstLetter: "A", isHot: false },
  { name: "鞍山", firstLetter: "A", isHot: false },
  { name: "安康", firstLetter: "A", isHot: false },
  { name: "阿克苏", firstLetter: "A", isHot: false },
  { name: "澳门", firstLetter: "A", isHot: true },

  // B
  { name: "北京", firstLetter: "B", isHot: true },
  { name: "包头", firstLetter: "B", isHot: false },
  { name: "保定", firstLetter: "B", isHot: false },
  { name: "本溪", firstLetter: "B", isHot: false },
  { name: "蚌埠", firstLetter: "B", isHot: false },
  { name: "北海", firstLetter: "B", isHot: false },
  { name: "宝鸡", firstLetter: "B", isHot: false },
  { name: "白城", firstLetter: "B", isHot: false },

  // C
  { name: "重庆", firstLetter: "C", isHot: true },
  { name: "成都", firstLetter: "C", isHot: true },
  { name: "长沙", firstLetter: "C", isHot: true },
  { name: "长春", firstLetter: "C", isHot: true },
  { name: "常州", firstLetter: "C", isHot: false },
  { name: "沧州", firstLetter: "C", isHot: false },
  { name: "承德", firstLetter: "C", isHot: false },
  { name: "长治", firstLetter: "C", isHot: false },
  { name: "常德", firstLetter: "C", isHot: false },
  { name: "郴州", firstLetter: "C", isHot: false },
  { name: "滁州", firstLetter: "C", isHot: false },
  { name: "赤峰", firstLetter: "C", isHot: false },

  // D
  { name: "大连", firstLetter: "D", isHot: true },
  { name: "东莞", firstLetter: "D", isHot: true },
  { name: "大庆", firstLetter: "D", isHot: false },
  { name: "丹东", firstLetter: "D", isHot: false },
  { name: "大同", firstLetter: "D", isHot: false },
  { name: "德州", firstLetter: "D", isHot: false },
  { name: "东营", firstLetter: "D", isHot: false },
  { name: "大理", firstLetter: "D", isHot: false },

  // F
  { name: "福州", firstLetter: "F", isHot: false },
  { name: "佛山", firstLetter: "F", isHot: true },
  { name: "抚顺", firstLetter: "F", isHot: false },
  { name: "阜阳", firstLetter: "F", isHot: false },
  { name: "抚州", firstLetter: "F", isHot: false },

  // G
  { name: "广州", firstLetter: "G", isHot: true },
  { name: "贵阳", firstLetter: "G", isHot: false },
  { name: "桂林", firstLetter: "G", isHot: false },
  { name: "赣州", firstLetter: "G", isHot: false },
  { name: "广元", firstLetter: "G", isHot: false },

  // H
  { name: "杭州", firstLetter: "H", isHot: true },
  { name: "哈尔滨", firstLetter: "H", isHot: true },
  { name: "合肥", firstLetter: "H", isHot: true },
  { name: "海口", firstLetter: "H", isHot: false },
  { name: "呼和浩特", firstLetter: "H", isHot: false },
  { name: "石家庄", firstLetter: "H", isHot: false },
  { name: "邯郸", firstLetter: "H", isHot: false },
  { name: "衡阳", firstLetter: "H", isHot: false },
  { name: "惠州", firstLetter: "H", isHot: false },
  { name: "湖州", firstLetter: "H", isHot: false },
  { name: "黄山", firstLetter: "H", isHot: false },
  { name: "香港", firstLetter: "H", isHot: true },

  // J
  { name: "济南", firstLetter: "J", isHot: false },
  { name: "金华", firstLetter: "J", isHot: false },
  { name: "嘉兴", firstLetter: "J", isHot: false },
  { name: "江门", firstLetter: "J", isHot: false },
  { name: "九江", firstLetter: "J", isHot: false },
  { name: "吉林", firstLetter: "J", isHot: false },
  { name: "济宁", firstLetter: "J", isHot: false },

  // K
  { name: "昆明", firstLetter: "K", isHot: false },
  { name: "开封", firstLetter: "K", isHot: false },

  // L
  { name: "兰州", firstLetter: "L", isHot: false },
  { name: "洛阳", firstLetter: "L", isHot: false },
  { name: "临沂", firstLetter: "L", isHot: false },
  { name: "柳州", firstLetter: "L", isHot: false },
  { name: "连云港", firstLetter: "L", isHot: false },
  { name: "廊坊", firstLetter: "L", isHot: false },

  // M
  { name: "绵阳", firstLetter: "M", isHot: false },
  { name: "牡丹江", firstLetter: "M", isHot: false },

  // N
  { name: "南京", firstLetter: "N", isHot: true },
  { name: "宁波", firstLetter: "N", isHot: true },
  { name: "南昌", firstLetter: "N", isHot: false },
  { name: "南宁", firstLetter: "N", isHot: false },
  { name: "南通", firstLetter: "N", isHot: false },
  { name: "南阳", firstLetter: "N", isHot: false },

  // Q
  { name: "青岛", firstLetter: "Q", isHot: true },
  { name: "泉州", firstLetter: "Q", isHot: false },
  { name: "秦皇岛", firstLetter: "Q", isHot: false },
  { name: "齐齐哈尔", firstLetter: "Q", isHot: false },

  // S
  { name: "上海", firstLetter: "S", isHot: true },
  { name: "深圳", firstLetter: "S", isHot: true },
  { name: "苏州", firstLetter: "S", isHot: true },
  { name: "沈阳", firstLetter: "S", isHot: true },
  { name: "石家庄", firstLetter: "S", isHot: false },
  { name: "汕头", firstLetter: "S", isHot: false },
  { name: "绍兴", firstLetter: "S", isHot: false },
  { name: "三亚", firstLetter: "S", isHot: false },

  // T
  { name: "天津", firstLetter: "T", isHot: true },
  { name: "太原", firstLetter: "T", isHot: false },
  { name: "台北", firstLetter: "T", isHot: true },
  { name: "台中", firstLetter: "T", isHot: false },
  { name: "台南", firstLetter: "T", isHot: false },
  { name: "唐山", firstLetter: "T", isHot: false },
  { name: "泰安", firstLetter: "T", isHot: false },

  // W
  { name: "武汉", firstLetter: "W", isHot: true },
  { name: "无锡", firstLetter: "W", isHot: false },
  { name: "温州", firstLetter: "W", isHot: false },
  { name: "乌鲁木齐", firstLetter: "W", isHot: false },
  { name: "潍坊", firstLetter: "W", isHot: false },
  { name: "芜湖", firstLetter: "W", isHot: false },

  // X
  { name: "西安", firstLetter: "X", isHot: true },
  { name: "厦门", firstLetter: "X", isHot: true },
  { name: "徐州", firstLetter: "X", isHot: false },
  { name: "西宁", firstLetter: "X", isHot: false },
  { name: "襄阳", firstLetter: "X", isHot: false },
  { name: "湘潭", firstLetter: "X", isHot: false },

  // Y
  { name: "银川", firstLetter: "Y", isHot: false },
  { name: "烟台", firstLetter: "Y", isHot: false },
  { name: "扬州", firstLetter: "Y", isHot: false },
  { name: "宜昌", firstLetter: "Y", isHot: false },
  { name: "岳阳", firstLetter: "Y", isHot: false },

  // Z
  { name: "郑州", firstLetter: "Z", isHot: true },
  { name: "珠海", firstLetter: "Z", isHot: false },
  { name: "中山", firstLetter: "Z", isHot: false },
  { name: "淄博", firstLetter: "Z", isHot: false },
  { name: "湛江", firstLetter: "Z", isHot: false },
  { name: "株洲", firstLetter: "Z", isHot: false },
  { name: "遵义", firstLetter: "Z", isHot: false }
];

// 导出数据
if (typeof module !== 'undefined' && module.exports) {
  module.exports = chineseCities;
}

// 浏览器环境下的全局变量
if (typeof window !== 'undefined') {
  window.chineseCities = chineseCities;
}

// 获取热门城市的辅助函数
function getHotCities() {
  return chineseCities.filter(city => city.isHot);
}

// 按首字母分组的辅助函数
function getCitiesByLetter() {
  const grouped = {};
  chineseCities.forEach(city => {
    if (!grouped[city.firstLetter]) {
      grouped[city.firstLetter] = [];
    }
    grouped[city.firstLetter].push(city);
  });
  return grouped;
}

// 导出辅助函数
if (typeof module !== 'undefined' && module.exports) {
  module.exports.getHotCities = getHotCities;
  module.exports.getCitiesByLetter = getCitiesByLetter;
}

if (typeof window !== 'undefined') {
  window.getHotCities = getHotCities;
  window.getCitiesByLetter = getCitiesByLetter;
}
